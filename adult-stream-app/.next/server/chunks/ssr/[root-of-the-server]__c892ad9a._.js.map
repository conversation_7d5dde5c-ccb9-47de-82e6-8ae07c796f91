{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryHeader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, lazy, Suspense } from 'react';\nimport Link from 'next/link';\n\n// Lazy load mobile menu for better performance\nconst MobileMenu = lazy(() => import('./MobileMenu').then(module => ({ default: module.MobileMenu })));\n\ninterface CategoryHeaderProps {\n  className?: string;\n}\n\nexport function CategoryHeader({ className }: CategoryHeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <>\n    <header className={`flex items-center justify-between whitespace-nowrap border-b border-solid border-border px-4 sm:px-6 lg:px-10 py-3 ${className || ''}`}>\n      {/* Left side - Logo and Navigation */}\n      <div className=\"flex items-center gap-4 md:gap-8\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center gap-2 text-white\">\n          <svg className=\"h-6 w-6 text-[#E92933]\" fill=\"none\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path\n              clipRule=\"evenodd\"\n              d=\"M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z\"\n              fill=\"currentColor\"\n              fillRule=\"evenodd\"\n            />\n          </svg>\n          <h2 className=\"text-white text-xl font-bold leading-tight tracking-[-0.015em]\">Streamr</h2>\n        </Link>\n\n        {/* Navigation - Hidden on mobile */}\n        <nav className=\"hidden md:flex items-center gap-6\">\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"/\"\n          >\n            Home\n          </Link>\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"/categories\"\n          >\n            Categories\n          </Link>\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"#\"\n          >\n            Models\n          </Link>\n          <Link\n            className=\"text-gray-300 hover:text-white text-sm font-medium leading-normal transition-colors\"\n            href=\"#\"\n          >\n            Premium\n          </Link>\n        </nav>\n      </div>\n\n      {/* Right side - Search, Bookmarks, User */}\n      <div className=\"flex flex-1 items-center justify-end gap-2 sm:gap-4\">\n        {/* Search Bar */}\n        <label className=\"relative flex-1 max-w-xs\">\n          <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n            <svg className=\"h-5 w-5 text-text-secondary\" fill=\"currentColor\" height=\"24px\" viewBox=\"0 0 256 256\" width=\"24px\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z\" />\n            </svg>\n          </div>\n          <input\n            className=\"form-input block w-full rounded-lg border-none bg-background-input py-2.5 pl-10 pr-3 text-white placeholder:text-text-placeholder focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background-primary sm:text-sm\"\n            placeholder=\"Search videos...\"\n            type=\"text\"\n          />\n        </label>\n\n        {/* Bookmarks Button */}\n        <button\n          className=\"flex items-center justify-center rounded-lg p-2.5 text-white transition-colors hover:bg-background-input focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background-primary\"\n          aria-label=\"Bookmarks\"\n        >\n          <svg fill=\"currentColor\" height=\"20px\" viewBox=\"0 0 256 256\" width=\"20px\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M184,32H72A16,16,0,0,0,56,48V224a8,8,0,0,0,12.24,6.78L128,193.43l59.77,37.35A8,8,0,0,0,200,224V48A16,16,0,0,0,184,32Zm0,177.57-51.77-32.35a8,8,0,0,0-8.48,0L72,209.57V48H184Z\" />\n          </svg>\n        </button>\n\n        {/* User Avatar */}\n        <div\n          className=\"aspect-square size-10 rounded-full bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: 'url(\"https://lh3.googleusercontent.com/aida-public/AB6AXuBrgo5NeXaf1PG9RgXgIDK3n5IPlb1GgzT75g1TjaBc0tAUMmUFsRjjTEIXpfsiCEBqkNPgniynMop2KNEbUBljuJlMbP6n_vkbsloyQSZVm0gixenHSGFS5-l8jkYeUaTme6FsAsT9iaNWj5h3M4oO7uBSZxHz2qf44FvvOan7QOnGQql8Gx74w4UCL70LlrKCxkil9dZD1IUd6BKKL5IKtQLbHmK1JY1E9vSfFUkFUsECZPI6qvLEFDBbsU1abUikZViSSqdXcZHx\")'\n          }}\n        />\n\n        {/* Mobile Menu Button */}\n        <button\n          className=\"md:hidden text-gray-300 hover:text-white p-2.5\"\n          aria-label=\"Open menu\"\n          onClick={() => setIsMobileMenuOpen(true)}\n        >\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M4 6h16M4 12h16m-7 6h7\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" />\n          </svg>\n        </button>\n      </div>\n    </header>\n\n    {/* Mobile Menu */}\n    {isMobileMenuOpen && (\n      <Suspense fallback={null}>\n        <MobileMenu\n          isOpen={isMobileMenuOpen}\n          onClose={() => setIsMobileMenuOpen(false)}\n        />\n      </Suspense>\n    )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,+CAA+C;AAC/C,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,IAAM,0IAAuB,IAAI,CAAC,CAAA,SAAU,CAAC;YAAE,SAAS,OAAO,UAAU;QAAC,CAAC;AAM5F,SAAS,eAAe,EAAE,SAAS,EAAuB;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BACA,8OAAC;gBAAO,WAAW,CAAC,mHAAmH,EAAE,aAAa,IAAI;;kCAExJ,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAA<PERSON>,SAAQ;wCAAY,OAAM;kDAC5E,cAAA,8OAAC;4CACC,UAAS;4CACT,GAAE;4CACF,MAAK;4CACL,UAAS;;;;;;;;;;;kDAGb,8OAAC;wCAAG,WAAU;kDAAiE;;;;;;;;;;;;0CAIjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,WAAU;wCACV,MAAK;kDACN;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA8B,MAAK;4CAAe,QAAO;4CAAO,SAAQ;4CAAc,OAAM;4CAAO,OAAM;sDACtH,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,8OAAC;wCACC,WAAU;wCACV,aAAY;wCACZ,MAAK;;;;;;;;;;;;0CAKT,8OAAC;gCACC,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAI,MAAK;oCAAe,QAAO;oCAAO,SAAQ;oCAAc,OAAM;oCAAO,OAAM;8CAC9E,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAKZ,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,iBAAiB;gCACnB;;;;;;0CAIF,8OAAC;gCACC,WAAU;gCACV,cAAW;gCACX,SAAS,IAAM,oBAAoB;0CAEnC,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;oCAAY,OAAM;8CACnF,cAAA,8OAAC;wCAAK,GAAE;wCAAyB,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOjG,kCACC,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,UAAU;0BAClB,cAAA,8OAAC;oBACC,QAAQ;oBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Projects/release/adult-stream-app/src/components/category/CategoryLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { CategoryHeader } from './CategoryHeader';\n\ninterface CategoryLayoutProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport function CategoryLayout({ children, className }: CategoryLayoutProps) {\n  return (\n    <div\n      className={`relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden bg-[#1A0B0C] dark ${className || ''}`}\n      style={{ fontFamily: '\"Plus Jakarta Sans\", \"Noto Sans\", sans-serif' }}\n    >\n      <div className=\"layout-container flex h-full grow flex-col\">\n        {/* Header */}\n        <CategoryHeader />\n\n        {/* Main Content */}\n        <main className=\"flex flex-1 justify-center py-5 px-4 sm:px-6 lg:px-8\">\n          <div className=\"layout-content-container w-full max-w-screen-xl flex-col\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUO,SAAS,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAuB;IACzE,qBACE,8OAAC;QACC,WAAW,CAAC,oGAAoG,EAAE,aAAa,IAAI;QACnI,OAAO;YAAE,YAAY;QAA+C;kBAEpE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gJAAA,CAAA,iBAAc;;;;;8BAGf,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}